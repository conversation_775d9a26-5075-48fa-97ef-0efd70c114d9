import logging
import async<PERSON>
import os
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>, Optional
from concurrent.futures import Thr<PERSON>PoolExecutor, as_completed
from qdrant_client import QdrantClient
from qdrant_client import models
from qdrant_client.models import Filter, FieldCondition, MatchValue, KeywordIndexParams
from openai import OpenAI
import json

from .document_ingestion import DocumentIngestion

logger = logging.getLogger(__name__)

# System prompt for OpenAI (reusing from base.py)
GPT_SYSTEM_PROMPT = (
    "You are an AI assistant tasked with answering questions using the provided context. "
    "Your role is to provide accurate, context-based responses while being transparent about your confidence level.\n\n"
    "Response Guidelines:\n"
    "1. If the answer is present in the context:\n"
    "   - Provide a concise and accurate response\n"
    "   - Include confidence_score between 0-1 (1 being highest confidence) based on how well the context supports your answer\n"
    "   - List all relevant context indices and document IDs used\n\n"
    "2. If the information is insufficient:\n"
    "   - Respond with: 'I cannot answer with the provided context'\n"
    "   - Include confidence_score between 0-1 (1 meaning you are completely certain the answer cannot be found in the context)\n"
    "   - Set context indices and doc_ids as empty arrays\n\n"
    "RESPONSE_FORMAT_GUIDELINES: "
    "Your response must be in JSON format with the following structure:\n\n"
    "{\n"
    '  "answer": "Your generated answer",\n'
    '  "confidence_score": float between 0-1,\n'
    '  "used_context_indices": [indices of context snippets used],\n'
    '  "used_doc_ids": [document IDs of used snippets]\n'
    "}\n\n"
    "CRITICAL JSON REQUIREMENTS:\n"
    "- The 'answer' field must be a STRING, not an object or array\n"
    "- Use standard JSON number format (no underscores in numbers)\n"
    "- Ensure all strings are properly quoted\n"
    "- Validate JSON syntax before responding\n\n"
    "Remember to:\n"
    "- Think deeply about each context snippet you use\n"
    "- Verify all context indices and doc_ids\n"
    "- Provide accurate confidence scores based on context quality and completeness\n"
    "- Never use knowledge outside the provided context\n"
    "- Never mention the Chunk indices used or Confidence score in the generated answer.\n"
    "- Double-check your JSON formatting"
)


class LatticeDocumentRetrieval:
    """
    Document retrieval system for parallel querying using lattice headers with payload-based partitioning.
    
    Supports:
    - Parallel queries across N lattice headers × M documents
    - Payload-based partitioning by doc_name for optimal performance
    - Hybrid dense + sparse embeddings with RRF fusion
    - OpenAI answer generation with JSON response format
    """
    
    def __init__(self, 
                 qdrant_url: str = "https://3312ccd1-6af5-4b15-8e3e-60ddbdd7b38a.europe-west3-0.gcp.cloud.qdrant.io:6333/",
                 openai_api_key: str = None):
        """
        Initialize the retrieval system.
        
        Args:
            qdrant_url: Qdrant server URL
            openai_api_key: OpenAI API key (defaults to env var)
        """
        self.qdrant_url = qdrant_url
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        self.doc_ingestion = DocumentIngestion()
        self._initialized = False
        
    def _initialize_models(self):
        """Initialize embedding models if not already done."""
        if not self._initialized:
            logger.info("Initializing embedding models...")
            self.doc_ingestion.init_dense_embedding_model()
            self.doc_ingestion.init_sparse_embedding_model()
            self._initialized = True
            logger.info("Embedding models initialized successfully")
    
    def _get_qdrant_client(self):
        """Get properly configured Qdrant client with error handling."""
        qdrant_api_key = os.getenv("QDRANT_API_KEY")
        if not qdrant_api_key:
            raise ValueError("QDRANT_API_KEY environment variable not set!")
        
        client = QdrantClient(
            url=self.qdrant_url, 
            api_key=qdrant_api_key,
            timeout=120,
            prefer_grpc=False,
        )
        
        return client
    
    def setup_collection_for_multitenancy(self, collection_name: str):
        """
        Set up collection with payload-based partitioning optimized for doc_name filtering.
        
        This implements Qdrant's multitenancy best practices:
        - Uses payload-based partitioning instead of multiple collections
        - Optimizes HNSW config for tenant-based queries
        - Creates keyword index with is_tenant=True for doc_name field
        """
        try:
            client = self._get_qdrant_client()
            
            # Check if collection exists
            try:
                collection_info = client.get_collection(collection_name)
                logger.info(f"Collection '{collection_name}' already exists with {collection_info.points_count} points")
                
                # Check if doc_name index exists with tenant optimization
                try:
                    client.create_payload_index(
                        collection_name=collection_name,
                        field_name="doc_name",
                        field_schema=KeywordIndexParams(
                            type="keyword",
                            is_tenant=True,  # Optimize for tenant-based queries
                        ),
                    )
                    logger.info("✅ doc_name index created/updated with tenant optimization")
                except Exception as e:
                    logger.info(f"doc_name index may already exist: {e}")
                
                return True
                
            except Exception:
                logger.info(f"Collection '{collection_name}' does not exist, creating with multitenancy config...")
                
                # Create collection with multitenancy-optimized HNSW config
                client.create_collection(
                    collection_name=collection_name,
                    vectors_config={
                        "dense": models.VectorParams(
                            size=1024,  # Jina v3 embedding size
                            distance=models.Distance.COSINE
                        )
                    },
                    sparse_vectors_config={
                        "sparse": models.SparseVectorParams(
                            modifier=models.Modifier.IDF,
                        )
                    },
                    # Multitenancy optimization: payload-based partitioning
                    hnsw_config=models.HnswConfigDiff(
                        payload_m=16,  # Enable payload-based indexing
                        m=0,           # Disable global index for better tenant isolation
                    ),
                )
                
                # Create tenant-optimized index for doc_name field
                client.create_payload_index(
                    collection_name=collection_name,
                    field_name="doc_name",
                    field_schema=KeywordIndexParams(
                        type="keyword",
                        is_tenant=True,  # Co-locate vectors of same tenant for better performance
                    ),
                )
                
                logger.info(f"✅ Collection '{collection_name}' created with multitenancy optimization")
                return True
                
        except Exception as e:
            logger.error(f"Failed to setup collection for multitenancy: {str(e)}")
            return False
    
    def search_qdrant_with_doc_filter(
        self,
        query: str,
        doc_name: str,
        collection_name: str,
        k: int = 5
    ) -> List[Dict]:
        """
        Search Qdrant with document name filtering using hybrid retrieval and payload-based partitioning.
        
        Args:
            query: The search query (lattice header)
            doc_name: Document name to filter by (pdf path) - acts as tenant ID
            collection_name: Name of the Qdrant collection
            k: Number of results to return
            
        Returns:
            List of retrieved chunks with text, doc_id, and score
        """
        try:
            # Ensure models are initialized
            self._initialize_models()
            
            # Get Qdrant client
            client = self._get_qdrant_client()
            
            # Ensure collection is properly configured for multitenancy
            if not self.setup_collection_for_multitenancy(collection_name):
                logger.error("Failed to setup collection for multitenancy")
                return []
            
            # Generate embeddings for the query
            logger.debug(f"Generating embeddings for query: '{query}' in doc: '{doc_name}'")
            
            # Get dense embedding
            dense_embedding = self.doc_ingestion.get_dense_embeddings(query)
            if dense_embedding is None:
                logger.error(f"Failed to generate dense embedding for query: {query}")
                return []
            
            # Get sparse embedding (optional - system can work with dense only)
            sparse_embedding_obj = self.doc_ingestion.get_sparse_embeddings(query)
            use_sparse = sparse_embedding_obj is not None
            
            if use_sparse:
                # Convert sparse embedding to dict format expected by Qdrant
                sparse_embedding = {
                    'indices': sparse_embedding_obj.indices,
                    'values': sparse_embedding_obj.values
                }
                logger.debug(f"Using hybrid retrieval (dense + sparse) for query: {query}")
            else:
                logger.warning(f"Sparse embedding failed for query '{query}', using dense-only retrieval")
                sparse_embedding = None
            
            # Perform hybrid retrieval with doc_name tenant filtering
            logger.debug(f"Performing tenant-optimized hybrid retrieval for tenant: {doc_name}")
            
            # Set up prefetch queries - adapt based on available embedding types
            prefetch = [
                models.Prefetch(
                    query=dense_embedding,
                    using="dense",
                    limit=k,
                ),
            ]
            
            # Add sparse query only if sparse embeddings are available
            if use_sparse and sparse_embedding:
                prefetch.append(
                    models.Prefetch(
                        query=models.SparseVector(**sparse_embedding),
                        using="sparse",
                        limit=k,
                    )
                )

            # Query with appropriate fusion and tenant-based filtering
            # The is_tenant=True index will optimize this query significantly
            if use_sparse and len(prefetch) > 1:
                # Use RRF fusion for hybrid retrieval
                response = client.query_points(
                    collection_name=collection_name,
                    prefetch=prefetch,
                    query=models.FusionQuery(
                        fusion=models.Fusion.RRF,
                    ),
                    query_filter=Filter(
                        must=[
                            FieldCondition(key="doc_name", match=MatchValue(value=doc_name)),
                        ]
                    ),
                    with_payload=True,
                    limit=k,
                )
            else:
                # Use dense-only retrieval
                response = client.query_points(
                    collection_name=collection_name,
                    query=dense_embedding,
                    using="dense",
                    query_filter=Filter(
                        must=[
                            FieldCondition(key="doc_name", match=MatchValue(value=doc_name)),
                        ]
                    ),
                    with_payload=True,
                    limit=k,
                )

            # Process and return results
            results = [
                {
                    "text": point.payload["text"],
                    "doc_id": point.payload["doc_id"],
                    "doc_name": point.payload["doc_name"],
                    "score": point.score,
                }
                for point in response.points
            ]
            
            logger.debug(f"Retrieved {len(results)} chunks for query '{query}' in doc '{doc_name}'")
            return results
            
        except Exception as e:
            logger.error(f"Error in tenant-optimized Qdrant search for query '{query}' in doc '{doc_name}': {str(e)}")
            return []
    
    def generate_answer_with_context(
        self, 
        query: str, 
        search_results: List[Dict]
    ) -> Dict:
        """
        Generate an answer using OpenAI based on search results.
        
        Args:
            query: The original query (lattice header)
            search_results: List of search results from Qdrant
            
        Returns:
            Dictionary with answer, confidence_score, used_context_indices, used_doc_ids
        """
        try:
            if not self.openai_api_key:
                raise ValueError("OpenAI API key not provided")
            
            # Initialize OpenAI client
            client = OpenAI(api_key=self.openai_api_key)
            
            # Format the search results into context
            context = self._format_search_context(search_results)
            
            # Create user prompt
            user_prompt = f"Question: {query}\n\nContext:\n{context}"
            
            messages = [
                {"role": "system", "content": GPT_SYSTEM_PROMPT},
                {"role": "user", "content": user_prompt},
            ]
            
            # Call OpenAI API
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                temperature=0.1,
                messages=messages,
                max_tokens=1000
            )
            
            generated_response = response.choices[0].message.content.strip()
            logger.debug(f"OpenAI response generated for query: {query}")
            
            # Parse JSON response
            try:
                parsed_response = json.loads(generated_response)
                return parsed_response
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse OpenAI JSON response: {e}")
                return {
                    "answer": "Error parsing response",
                    "confidence_score": 0.0,
                    "used_context_indices": [],
                    "used_doc_ids": []
                }
            
        except Exception as e:
            logger.error(f"Error generating answer for query '{query}': {str(e)}")
            return {
                "answer": f"Error generating answer: {str(e)}",
                "confidence_score": 0.0,
                "used_context_indices": [],
                "used_doc_ids": []
            }
    
    def _format_search_context(self, search_results: List[Dict]) -> str:
        """
        Format search results into a context string for OpenAI.
        
        Args:
            search_results: List of search results from Qdrant
            
        Returns:
            Formatted context string
        """
        if not search_results:
            return "No relevant context found."
        
        context = ""
        for idx, result in enumerate(search_results):
            text = result.get("text", "")
            doc_id = result.get("doc_id", "unknown")
            doc_name = result.get("doc_name", "unknown")
            score = result.get("score", 0.0)
            
            context += f"Chunk {idx + 1} (Score: {score:.3f}):\n"
            context += f"Content: {text}\n"
            context += f"Document ID: {doc_id}\n"
            context += f"Document Name: {doc_name}\n"
            context += "-" * 50 + "\n"
        
        return context
    
    def parallel_lattice_query(
        self,
        lattice_headers: List[str],
        pdf_paths: List[str],
        collection_name: str,
        k: int = 5,
        max_workers: int = 10
    ) -> Dict[str, Dict[str, Dict]]:
        """
        Perform parallel queries for all combinations of lattice headers and documents.
        Uses payload-based partitioning for optimal performance.
        
        Args:
            lattice_headers: List of lattice headers to use as queries
            pdf_paths: List of PDF paths (doc_names) to filter by - these act as tenant IDs
            collection_name: Name of the Qdrant collection
            k: Number of chunks to retrieve per query
            max_workers: Maximum number of parallel workers
            
        Returns:
            Nested dictionary: {lattice_header: {doc_name: {answer, confidence_score, etc.}}}
        """
        logger.info(f"Starting tenant-optimized parallel lattice query: {len(lattice_headers)} headers × {len(pdf_paths)} documents = {len(lattice_headers) * len(pdf_paths)} total queries")
        
        results = {}
        
        # Create all query combinations
        query_tasks = []
        for header in lattice_headers:
            results[header] = {}
            for pdf_path in pdf_paths:
                query_tasks.append((header, pdf_path))
        
        # Execute queries in parallel with tenant optimization
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_query = {
                executor.submit(
                    self._execute_single_query,
                    header,
                    pdf_path,
                    collection_name,
                    k
                ): (header, pdf_path)
                for header, pdf_path in query_tasks
            }
            
            # Collect results as they complete
            completed = 0
            for future in as_completed(future_to_query):
                header, pdf_path = future_to_query[future]
                try:
                    result = future.result()
                    results[header][pdf_path] = result
                    completed += 1
                    
                    if completed % 10 == 0 or completed == len(query_tasks):
                        logger.info(f"Completed {completed}/{len(query_tasks)} tenant-optimized queries")
                        
                except Exception as e:
                    logger.error(f"Error in query for header '{header}' and doc '{pdf_path}': {str(e)}")
                    results[header][pdf_path] = {
                        "answer": f"Error executing query: {str(e)}",
                        "confidence_score": 0.0,
                        "used_context_indices": [],
                        "used_doc_ids": [],
                        "chunks_retrieved": 0
                    }
        
        logger.info(f"Completed all {len(query_tasks)} tenant-optimized parallel queries")
        return results
    
    def _execute_single_query(
        self,
        lattice_header: str,
        doc_name: str,
        collection_name: str,
        k: int
    ) -> Dict:
        """
        Execute a single query: search Qdrant + generate answer using tenant optimization.
        
        Args:
            lattice_header: The query string
            doc_name: Document name to filter by (tenant ID)
            collection_name: Qdrant collection name
            k: Number of chunks to retrieve
            
        Returns:
            Dictionary with answer, confidence_score, chunks_retrieved, etc.
        """
        try:
            # Step 1: Search Qdrant with tenant optimization
            search_results = self.search_qdrant_with_doc_filter(
                query=lattice_header,
                doc_name=doc_name,
                collection_name=collection_name,
                k=k
            )
            
            # Step 2: Generate answer with OpenAI
            answer_result = self.generate_answer_with_context(
                query=lattice_header,
                search_results=search_results
            )
            
            # Add metadata
            answer_result["chunks_retrieved"] = len(search_results)
            answer_result["lattice_header"] = lattice_header
            answer_result["doc_name"] = doc_name
            
            return answer_result
            
        except Exception as e:
            logger.error(f"Error executing single query for '{lattice_header}' in '{doc_name}': {str(e)}")
            return {
                "answer": f"Error executing query: {str(e)}",
                "confidence_score": 0.0,
                "used_context_indices": [],
                "used_doc_ids": [],
                "chunks_retrieved": 0,
                "lattice_header": lattice_header,
                "doc_name": doc_name
            }


# Convenience function for easy usage
def parallel_lattice_retrieval(
    lattice_headers: List[str],
    pdf_paths: List[str],
    collection_name: str = "document_chunks",
    qdrant_url: str = "https://23e22f8a-1a13-43e8-af6a-7db5deacc333.us-east4-0.gcp.cloud.qdrant.io:6333/",
    k: int = 5,
    max_workers: int = 10,
    openai_api_key: str = None
) -> Dict[str, Dict[str, Dict]]:
    """
    Convenience function for parallel lattice header retrieval with payload-based partitioning.
    
    Args:
        lattice_headers: List of lattice headers from extract_and_consolidate_lattice_headers
        pdf_paths: List of PDF paths to use as doc_name filters (tenant IDs)
        collection_name: Qdrant collection name
        qdrant_url: Qdrant server URL
        k: Number of chunks to retrieve per query
        max_workers: Maximum parallel workers
        openai_api_key: OpenAI API key (optional, uses env var if not provided)
        
    Returns:
        Nested dictionary: {lattice_header: {doc_name: {answer, confidence_score, etc.}}}
    """
    retrieval_system = LatticeDocumentRetrieval(
        qdrant_url=qdrant_url,
        openai_api_key=openai_api_key
    )
    
    return retrieval_system.parallel_lattice_query(
        lattice_headers=lattice_headers,
        pdf_paths=pdf_paths,
        collection_name=collection_name,
        k=k,
        max_workers=max_workers
    )


# Example usage
if __name__ == "__main__":
    # Example lattice headers (you would get these from extract_and_consolidate_lattice_headers)
    example_headers = [
        "Financial Performance",
        "Revenue Analysis", 
        "Risk Assessment",
        "Market Position"
    ]
    
    # Example PDF paths (doc_names/tenant IDs)
    example_pdf_paths = [
        "mp_materials/pdfs/amendment.pdf",
        "mp_materials/pdfs/contract.pdf",
        "mp_materials/pdfs/exhibit-3.1.pdf",
        "mp_materials/pdfs/exhibit-10.7.pdf"
    ]
    
    # Execute parallel retrieval with multitenancy optimization
    logger.info("Starting example parallel lattice retrieval with payload-based partitioning...")
    results = parallel_lattice_retrieval(
        lattice_headers=example_headers,
        pdf_paths=example_pdf_paths,
        collection_name="document_chunks",
        k=3,
        max_workers=5
    )
    
    # Display results
    print("\n" + "="*80)
    print("TENANT-OPTIMIZED PARALLEL LATTICE RETRIEVAL RESULTS")
    print("="*80)
    
    for header in results:
        print(f"\n📋 LATTICE HEADER: {header}")
        print("-" * 60)
        
        for doc_name in results[header]:
            doc_result = results[header][doc_name]
            print(f"\n📄 Document (Tenant): {doc_name}")
            print(f"Answer: {doc_result['answer']}")
            print(f"Confidence: {doc_result['confidence_score']:.2f}")
            print(f"Chunks Retrieved: {doc_result['chunks_retrieved']}")
            print(f"Used Doc IDs: {doc_result['used_doc_ids']}") 